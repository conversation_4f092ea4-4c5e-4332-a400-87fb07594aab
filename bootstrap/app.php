<?php

use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Route;
use Spatie\Multitenancy\Exceptions\NoCurrentTenant;
use Spatie\Multitenancy\Http\Middleware\NeedsTenant;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
        then: function () {
            Route::middleware('api')->prefix('api')->group(function () {
                Route::prefix('v1')->name('v1.')
                    ->group(__DIR__ . '/../routes/api/v1.php');
            });
        }
    )
    ->withMiddleware(function (Middleware $middleware): void {
        $middleware->append([
            NeedsTenant::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        $exceptions->renderable(function (NoCurrentTenant $e) {
            return response()->json(['message' => $e->getMessage()], 400);
        });
    })->create();
