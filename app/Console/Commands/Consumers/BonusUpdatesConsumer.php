<?php

namespace App\Console\Commands\Consumers;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use <PERSON>es\Kafka\Consumers\Consumer;
use Junges\Kafka\Contracts\ConsumerMessage;

/**
 * IMPORTANT: Run this command separately for each tenant. Due to method consume() is a blocking method
 */
class BonusUpdatesConsumer extends ConsumerCommand
{
    private const string BONUS_CACHE_KEY = 'bonus';
    private const int BONUS_CACHE_TTL = 60 * 60 * 24;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'consume:bonus-updates {--tenant=*}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Consume bonus updates from topic bonuses';

    protected function getTopicName(): string
    {
        return 'bonuses.cud';
    }

    protected function handleMessage(ConsumerMessage $message, Consumer $consumer): void
    {
        $action = $message->getHeaders()['action'] ?? null;

        $this->info("Received message with event: $action. BonusId: {$message->getKey()}");

        match ($action) {
            'updated', 'created' => $this->createOrUpdate($message),
            'deleted' => $this->delete($message),
            default => $this->unknownAction($action, $message->getKey()),
        };

//        new EventBonusUpdated($message->getBody());
    }

    private function createOrUpdate(ConsumerMessage $message): void
    {
        /** @var array{id: int} $bonus */
        $bonus = $message->getBody();

        Cache::put($this->getBonusCacheKey($bonus['id']), $bonus, self::BONUS_CACHE_TTL);
    }

    private function delete(ConsumerMessage $message): void
    {
        /** @var array{id: int} $bonus */
        $bonus = $message->getBody();

        Cache::forget($this->getBonusCacheKey($bonus['id']));
    }

    private function getBonusCacheKey(int $bonusId): string
    {
        return sprintf('%s:%s', self::BONUS_CACHE_KEY, $bonusId);
    }

    private function unknownAction(string $action, ?int $bonusId = null): void
    {
        $this->warn('Unknown action: ' . $action . ' for bonusId: ' . $bonusId);
        Log::info('Unknown action: ' . $action . ' for bonusId: ' . $bonusId);
    }
}
