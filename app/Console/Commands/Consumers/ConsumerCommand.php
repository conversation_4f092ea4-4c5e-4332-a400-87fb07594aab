<?php

namespace App\Console\Commands\Consumers;

use App\Multitenancy\Tenant;
use Carbon\Exceptions\Exception;
use Illuminate\Console\Command;
use <PERSON><PERSON>\Kafka\Consumers\Consumer;
use <PERSON><PERSON>\Kafka\Contracts\ConsumerMessage;
use <PERSON><PERSON>\Kafka\Exceptions\ConsumerException;
use <PERSON><PERSON>\Kafka\Facades\Kafka;
use Spatie\Multitenancy\Commands\Concerns\TenantAware;

/**
 * Move to promocode service
 * IMPORTANT: Run such commands separately for each tenant. Due to method consume() is a blocking method
 */
abstract class ConsumerCommand extends Command
{
    use TenantAware;

    /**
     * @return void
     * @throws Exception
     * @throws ConsumerException
     */
    public function handle(): void
    {
        $this->info(sprintf('Running the Consumer for tenant: %s', Tenant::current()->name));

        /** @var \RdKafka\KafkaConsumer $kafkaConsumer */
        $kafkaConsumer = app(\RdKafka\KafkaConsumer::class);
        $metadata = $kafkaConsumer->getMetadata(true, null, 60e3);

        $topicName = $this->getTopicName() . '.' . Tenant::current()->name;
        $topicExists = false;

        foreach ($metadata->getTopics() as $topic) {
            if ($topic->getTopic() === $topicName) {
                $topicExists = true;
                break;
            }
        }

        if (!$topicExists) {
            $conf = new \RdKafka\Conf();
            $producer = new \RdKafka\Producer($conf);
            $topic = $producer->newTopic($topicName);
            $topic->produce(RD_KAFKA_PARTITION_UA, 0, '');
            $producer->flush(1000);
        }



        $consumer = Kafka::consumer([$this->getTopicName() . '.' . Tenant::current()->name])
            ->withConsumerGroupId($this->getGroupId())
            ->withHandler(function (ConsumerMessage $message, Consumer $consumer) {
                $this->handleMessage($message, $consumer);
            })
            ->build();

        $this->info('Start consuming...');
        $consumer->consume();
    }

    protected function getGroupId(): string
    {
        return config('kafka.consumer_group_id') . ':tenant-' . Tenant::current()->id;
    }

    abstract protected function handleMessage(ConsumerMessage $message, Consumer $consumer): void;

    abstract protected function getTopicName(): string;
}
