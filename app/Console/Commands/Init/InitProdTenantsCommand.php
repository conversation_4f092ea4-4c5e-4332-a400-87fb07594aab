<?php

namespace App\Console\Commands\Init;

use App\Multitenancy\Tenant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use JsonException;
use Ramsey\Uuid\Uuid;

class InitProdTenantsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'init:prod-tenants';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Initialize production environment tenants';

    /**
     * @throws JsonException
     */
    public function handle(): void
    {
        Log::info('Running the InitProdTenantsCommand');

        if (!app()->environment('production')) {
            $this->warn('This command can only be run in the production environment.');

            return;
        }

        Tenant::query()->insertOrIgnore($this->getTenantsData());
    }

    /**
     * @throws JsonException
     */
    private function getTenantsData(): array
    {
        return [
            [
                'name' => '4rabet',
                'token' => Uuid::uuid4()->toString(),
                'connections' => json_encode([
                    'mysql' => [
                        'host' => '{{DB_TENANT1_HOST}}',
                        'port' => '{{DB_TENANT1_PORT}}',
                        'database' => '{{DB_TENANT1_DATABASE}}',
                        'username' => '{{DB_TENANT1_USERNAME}}',
                        'password' => '{{DB_TENANT1_PASSWORD}}',
                    ]
                ], JSON_THROW_ON_ERROR),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];
    }
}
