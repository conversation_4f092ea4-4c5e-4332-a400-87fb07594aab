<?php

namespace App\Console\Commands\Init;

use App\Multitenancy\Tenant;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use JsonException;
use Ramsey\Uuid\Uuid;

class InitDevTenantsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'init:dev-tenants';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Initialize development environment tenants';

    /**
     * @throws JsonException
     */
    public function handle(): void
    {
        Log::info('Running the InitDevTenantsCommand');

        if (!app()->environment('dev')) {
            $this->warn('This command can only be run in the development environment.');

            return;
        }

        Tenant::query()->insertOrIgnore($this->getTenantsData());
    }

    /**
     * @throws JsonException
     */
    private function getTenantsData(): array
    {
        return [
            [
                'name' => 'dev1',
                'token' => Uuid::uuid4()->toString(),
                'connections' => json_encode([
                    'mysql' => [
                        'host' => '{{DB_TENANT1_HOST}}',
                        'port' => '{{DB_TENANT1_PORT}}',
                        'database' => '{{DB_TENANT1_DATABASE}}',
                        'username' => '{{DB_TENANT1_USERNAME}}',
                        'password' => '{{DB_TENANT1_PASSWORD}}',
                    ]
                ], JSON_THROW_ON_ERROR),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'dev2',
                'token' => Uuid::uuid4()->toString(),
                'connections' => json_encode([
                    'mysql' => [
                        'host' => '{{DB_TENANT2_HOST}}',
                        'port' => '{{DB_TENANT2_PORT}}',
                        'database' => '{{DB_TENANT2_DATABASE}}',
                        'username' => '{{DB_TENANT2_USERNAME}}',
                        'password' => '{{DB_TENANT2_PASSWORD}}',
                    ]
                ], JSON_THROW_ON_ERROR),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];
    }
}
