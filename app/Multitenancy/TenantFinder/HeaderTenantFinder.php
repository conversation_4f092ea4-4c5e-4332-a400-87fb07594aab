<?php

declare(strict_types=1);

namespace App\Multitenancy\TenantFinder;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Date;
use Spatie\Multitenancy\Contracts\IsTenant;
use Spatie\Multitenancy\Models\Tenant;
use Spatie\Multitenancy\TenantFinder\TenantFinder;

class HeaderTenantFinder extends TenantFinder
{

    public function findForRequest(Request $request): ?Tenant
    {
        $tenantToken = $request->header('Tenant-Token');

        if (!$tenantToken) {
            return null;
        }

        /** @var \App\Multitenancy\Tenant $tenantModel */
        $tenantModel = app(IsTenant::class);

        $tenantData = $this->getTenantFromCacheOrRemember($tenantToken);

        if ($tenantData && $tenantData['name']) {
            $request->headers->set('tenant-name', $tenantData['name']);
        }

        return $tenantData ? (new $tenantModel())->forceFill($tenantData) : null;
    }

    private function getTenantFromCacheOrRemember(string $tenantToken)
    {
        if ($tenantData = Cache::get($this->getTenantCacheKey($tenantToken))) {
            return $tenantData;
        }

        /** @var \App\Multitenancy\Tenant $tenantModel */
        $tenantModel = app(IsTenant::class);

        $tenantData = $tenantModel::whereTenantToken($tenantToken)->first()?->toArray();

        if ($tenantData !== null) {
            Cache::put($this->getTenantCacheKey($tenantToken), $tenantData, Date::now()->addDay());
        }

        return $tenantData;
    }

    private function getTenantCacheKey(string $tenantToken): string
    {
        return 'tenant:' . $tenantToken;
    }
}
