<?php

declare(strict_types=1);

namespace App\Multitenancy;

use Illuminate\Database\Eloquent\Builder;
use Spatie\Multitenancy\Models\Tenant as BaseTenant;

/**
 * @property int $id
 * @property string $name
 * @property string $token
 * @property array $connections
 * @method static Tenant whereTenantToken(string $tenantId)
 * @uses self::scopeWhereTenantToken()
 *
 * @mixin Builder
 */
class Tenant extends BaseTenant
{
    protected $keyType = 'string';
    protected $guarded = [];

    protected $casts = [
        'connections' => 'array',
    ];

    public function scopeWhereTenantToken(Builder $query, string $tenantToken): Builder
    {
        return $query->where('token', $tenantToken);
    }
}
