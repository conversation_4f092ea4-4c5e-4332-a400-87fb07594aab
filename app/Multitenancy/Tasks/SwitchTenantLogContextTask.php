<?php

namespace App\Multitenancy\Tasks;

use Illuminate\Support\Facades\Log;
use Spatie\Multitenancy\Contracts\IsTenant;
use Spatie\Multitenancy\Models\Tenant;
use Spatie\Multitenancy\Tasks\SwitchTenantTask;

class SwitchTenantLogContextTask implements SwitchTenantTask
{
    /**
     * @param \App\Multitenancy\Tenant $tenant
     * @return void
     */
    public function makeCurrent(IsTenant $tenant): void
    {
        Log::withContext([
            'tenant_name' => $tenant->name,
        ]);
    }

    public function forgetCurrent(): void
    {
    }
}
