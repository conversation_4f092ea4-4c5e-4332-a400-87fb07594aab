<?php

declare(strict_types=1);

namespace App\Multitenancy\Tasks;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\DB;
use Spatie\Multitenancy\Concerns\UsesMultitenancyConfig;
use Spatie\Multitenancy\Contracts\IsTenant;
use Spatie\Multitenancy\Exceptions\InvalidConfiguration;
use Spatie\Multitenancy\Models\Tenant;
use Spatie\Multitenancy\Tasks\SwitchTenantTask;

class SwitchTenantDatabaseTask implements SwitchTenantTask
{
    use UsesMultitenancyConfig;

    /**
     * @throws InvalidConfiguration
     */
    public function makeCurrent(IsTenant $tenant): void
    {
        $this->setTenantConnectionDatabaseName($tenant->getAttribute('connections'));
    }

    /**
     * @throws InvalidConfiguration
     */
    public function forgetCurrent(): void
    {
        $this->setTenantConnectionDatabaseName(null);
    }

    /**
     * @param array|null $connections
     * @return void
     * @throws InvalidConfiguration
     */
    protected function setTenantConnectionDatabaseName(?array $connections): void
    {
        $tenantConnectionName = $this->tenantDatabaseConnectionName();

        if (is_null($connections)) {
            return;
        }

        $connections = $this->recursivelyReplaceEnv($connections);
        $connections = array_replace_recursive(config("database.connections"), $connections);

        config(["database.connections" => $connections]);

        foreach ($connections as $connectionName => $connection) {
            config(["database.connections.$connectionName" => $connection]);

            DB::purge($connectionName);
        }

        if (is_null(config("database.connections.$tenantConnectionName"))) {
            throw InvalidConfiguration::tenantConnectionDoesNotExist($tenantConnectionName);
        }

        // Octane will have an old `db` instance in the Model::$resolver.
        Model::setConnectionResolver(app('db'));
    }

    private function recursivelyReplaceEnv(array $connections): array
    {
        foreach ($connections as $key => $value) {
            if (is_array($value)) {
                $connections[$key] = $this->recursivelyReplaceEnv($value);
            } elseif (is_string($value) && $this->hasEnvVariable($value)) {
                $connections[$key] = $this->replaceEnvVariables($value);
            }
        }

        return $connections;
    }

    private function hasEnvVariable(string $value): bool
    {
        return str_contains($value, '{{') && str_contains($value, '}}');
    }

    private function replaceEnvVariables(string $value): string
    {
        $envName = str_replace(['{{', '}}'], '', $value);
        return env($envName) ?? $value;
    }
}
