<?php

namespace App\Multitenancy\Tasks;

use Illuminate\Support\Facades\Config;
use Spatie\Multitenancy\Contracts\IsTenant;
use Spatie\Multitenancy\Models\Tenant;
use Spatie\Multitenancy\Tasks\SwitchTenantTask;

class SwitchTenantFilesystemTask implements SwitchTenantTask
{
    protected array $appendTenantDirTo = [
        'public.root',
        'local.root',
    ];

    public function __construct()
    {
    }

    /**
     * @param \App\Multitenancy\Tenant $tenant
     * @return void
     */
    public function makeCurrent(IsTenant $tenant): void
    {
        foreach ($this->appendTenantDirTo as $value) {
            $configPath = 'filesystems.disks.' . $value;
            $baseDir = Config::get($configPath);

            Config::set($configPath , $baseDir . "/$tenant->token" );
        }
    }

    public function forgetCurrent(): void
    {

    }
}
