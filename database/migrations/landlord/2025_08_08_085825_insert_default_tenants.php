<?php

use App\Multitenancy\Tenant;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (app()->environment('local')) {
            Tenant::query()->insert($this->getLocalTenants());
        }
    }

    /**
     * @return array[]
     */
    public function getLocalTenants(): array
    {
        return [
            [
                'name' => 'local1',
                'token' => '1c7e4536-1728-4ed5-be89-1c19543b14e6',
                'connections' => json_encode([
                    // This property (mysql) may contain an array with multiple mysql connections if it necessary
                    'mysql' => [
                        'host' => env('DB_TENANT1_HOST', '127.0.0.1'),
                        'port' => '{{DB_TENANT1_PORT}}',
                        'database' => '{{DB_TENANT1_DATABASE}}',
                        'username' => '{{DB_TENANT1_USERNAME}}',
                        'password' => '{{DB_TENANT1_PASSWORD}}',
                    ]
                ], JSON_THROW_ON_ERROR),
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'local2',
                'token' => '5373e9e2-b4f1-45c2-88fe-5edf18eece42',
                'connections' => json_encode([
                    'mysql' => [
                        'host' => env('DB_TENANT2_HOST', '127.0.0.1'),
                        'port' => '{{DB_TENANT2_PORT}}',
                        'database' => '{{DB_TENANT2_DATABASE}}',
                        'username' => '{{DB_TENANT2_USERNAME}}',
                        'password' => '{{DB_TENANT2_PASSWORD}}',
                    ]
                ], JSON_THROW_ON_ERROR),
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (app()->environment('local')) {
            $tenantTokens = array_column($this->getLocalTenants(), 'token');

            Tenant::query()->whereIn('token', $tenantTokens)->delete();
        }
    }
};
