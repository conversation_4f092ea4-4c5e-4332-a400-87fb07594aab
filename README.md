
## Мультитинантность

### Миграции

- Для запуска миграций в landlord базу нужно использовать комманду

```bash
  php artisan migrate --path=database/migrations/landlord --database=landlord
```

- Для запуска миграций во всех базах тинантов используется следующая комманду

```bash  
    php artisan tenants:artisan "migrate --database=<tenant_connection>" # default tenant_connection = mysql
```

### Комманды

Для добавления мультитинантности в коммандах, необходимо использовать Trait - TenantAware в классе комманды

```php
// ...
use Spatie\Multitenancy\Commands\Concerns\TenantAware;

class YourFavoriteCommand extends Command
{
    use TenantAware;

    protected $signature = 'your-favorite-command {--tenant=*}';

    public function handle()
    {
        return $this->line('The tenant is '. Tenant::current()->name);
    }
}

```

Документация тут - https://spatie.be/docs/laravel-multitenancy/v3/advanced-usage/making-artisan-commands-tenant-aware

### Job-ы

Для запуска мультитинантных джоб нужно имплементировать интерфейс TenantAware. Пример:

```php
use Illuminate\Contracts\Queue\ShouldQueue;
use Spatie\Multitenancy\Jobs\TenantAware;

class TestJob implements ShouldQueue, TenantAware
{
    public function handle()
    {
        // do the work
    }
}
```

Документация тут - https://spatie.be/docs/laravel-multitenancy/v3/basic-usage/making-queues-tenant-aware

### Разворачивание на локальной среде

1. Запустить composer install для установки недостающих пакетов

```bash
    composer install
```

2. Прописать переменные окружения в .env

```dotenv
DB_LANDLORD_DATABASE=promocode_landlord_db
DB_LANDLORD_HOST=mysql
DB_LANDLORD_USERNAME=root
DB_LANDLORD_PASSWORD=secret

#tenants db
DB_TENANT1_HOST=mysql
DB_TENANT1_PORT=3306
DB_TENANT1_USERNAME=root
DB_TENANT1_PASSWORD=secret
DB_TENANT1_DATABASE=bonus_management

DB_TENANT2_HOST=mysql
DB_TENANT2_PORT=3306
DB_TENANT2_USERNAME=root
DB_TENANT2_PASSWORD=secret
DB_TENANT2_DATABASE=bonus_management
# ...
```

3. Запустить комманду landlord миграций

```bash
php artisan migrate --path=database/migrations/landlord --database=landlord 
```

4. Запустить миграции для тинантов если нужно

```bash  
    php artisan tenants:artisan "migrate --database=mysql"
```

5. В запросах через postman добавить заголовок Tenant-Token: local-tenant-1
